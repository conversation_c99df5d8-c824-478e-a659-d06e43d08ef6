import UIKit
import React
// 如果使用的是 React Native 0.68 或更高版本，使用以下导入
// import React_Native_Bridging

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
  var window: UIWindow?

  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> Bool {
    let rootView = RCTRootView(
      bundleURL: Bundle.main.url(forResource: "main", withExtension: "jsbundle") ?? RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index"),
      moduleName: "ShoppingMallApp",
      initialProperties: nil,
      launchOptions: launchOptions
    )
    
    let rootViewController = UIViewController()
    rootViewController.view = rootView
    
    window = UIWindow(frame: UIScreen.main.bounds)
    window?.rootViewController = rootViewController
    window?.makeKeyAndVisible()
    
    return true
  }
}
