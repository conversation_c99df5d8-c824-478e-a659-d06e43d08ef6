const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const os = require('os');

if (!os.availableParallelism) {
  os.availableParallelism = () => {
    return os.cpus().length;
  };
}

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  // 明确设置 maxWorkers 避免使用 os.availableParallelism
  maxWorkers: 2,
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
