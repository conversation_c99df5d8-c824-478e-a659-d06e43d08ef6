import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Platform } from 'react-native';

// 导入导航器和页面
import TabNavigator from './TabNavigator';
import LoginScreen from '../screens/LoginScreen';
import SplashScreen from '../screens/SplashScreen';

const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName="Splash"
      screenOptions={{
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        // iOS风格的页面切换动画
        animation: Platform.OS === 'ios' ? 'slide_from_right' : 'slide_from_right',
        presentation: 'card',
        gestureEnabled: true,
        gestureDirection: 'horizontal',
      }}>
      {/* 启动页 */}
      <Stack.Screen
        name="Splash"
        component={SplashScreen}
        options={{
          headerShown: false, // 启动页不显示header
          gestureEnabled: false, // 启动页禁用手势返回
        }}
      />

      {/* 主要的Tab导航 */}
      <Stack.Screen
        name="Main"
        component={TabNavigator}
        options={{
          headerShown: false, // 隐藏Stack的header，使用Tab的header
          gestureEnabled: false, // 主页面禁用手势返回
          animation: 'none', // 禁用从启动页到主页面的动画
        }}
      />

      {/* 登录页面 */}
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{
          title: '登录',
          headerBackTitle: '返回',
          // iOS风格的右进左出动画
          animation: 'slide_from_right',
          presentation: 'card',
        }}
      />
    </Stack.Navigator>
  );
};

export default AppNavigator;
